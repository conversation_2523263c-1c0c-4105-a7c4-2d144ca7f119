<template>
    <ui-view type="content" enable-search v-if="isInitialized">
        <div class="accounting-reports-partner-balances">
            <div class="partner-balances-left">
                <ui-form
                    :schema="payloadSchema"
                    :model="payload"
                    no-inner-padding
                    no-padding
                    @changed="handlePayloadChange"
                >
                    <el-scrollbar class="p15">
                        <ui-field name="startDate" label-position="top" />
                        <ui-field name="endDate" label-position="top" />
                        <ui-field
                            name="partnerTypes"
                            :options="typeOptions"
                            translate-labels
                            label-position="top"
                            v-show="!$params('type')"
                        />
                        <ui-field
                            name="partnerGroupIds"
                            collection="kernel.partner-groups"
                            :filters="{type: !!$params('type') ? $params('type') : {$in: ['customer', 'vendor']}}"
                            label-position="top"
                        />
                        <ui-field name="partnerTags" :options="partnerTagsOptions" label-position="top" />
                        <ui-field
                            name="partnerId"
                            collection="kernel.partners"
                            view="partners.partners"
                            :filters="{type: !!$params('type') ? $params('type') : {$in: ['customer', 'vendor']}}"
                            disable-create
                            disable-detail
                            label-position="top"
                        />
                        <ui-field
                            name="currencyIds"
                            collection="kernel.currencies"
                            :filters="{name: {$ne: systemCurrency.name}}"
                            label-position="top"
                        />
                        <ui-field name="branchIds" collection="kernel.branches" label-position="top" />
                        <ui-field
                            name="financialProjectIds"
                            collection="kernel.financial-projects"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            label-position="top"
                        />
                        <ui-field
                            name="salespersonId"
                            collection="kernel.partners"
                            view="partners.partners"
                            :filters="{type: 'employee'}"
                            disable-create
                            disable-detail
                            label-position="top"
                            v-show="$params('type') !== 'employee'"
                        />
                        <ui-field
                            name="scope"
                            :options="scopeOptions"
                            label-position="top"
                            v-show="!!$setting('system.scopes')"
                        />
                        <ui-field name="groupByCurrency" label="hide" :active-text="$t('Group by currency')" />
                        <ui-field name="groupByBranch" label="hide" :active-text="$t('Group by branch')" />
                        <ui-field name="groupByProject" label="hide" :active-text="$t('Group by project')" />
                        <ui-field
                            name="groupBySalesperson"
                            label="hide"
                            :active-text="$t('Group by salesperson')"
                            v-show="$params('type') !== 'employee'"
                        />
                        <ui-field
                            name="hideZeroBalances"
                            label="hide"
                            :active-text="$t('Hide Zero Balances')"
                        />
                    </el-scrollbar>
                </ui-form>
            </div>

            <div class="partner-balances-content">
                <ui-table
                    :key="resultKey"
                    id="accounting.reports-partner-balances"
                    :get-rows="getRows"
                    :summary-row="summaryRow"
                    row-model="serverSide"
                    :columns="columns"
                    :search="$params('search')"
                    :options="tableOptions"
                    :enable-selection="true"
                    :single-select="true"
                />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {rawMongoQuery} from 'framework/helpers';

export default {
    data: () => ({
        payload: {},
        currencies: [],
        partnerTags: [],
        typeOptions: [
            {value: 'customer', label: 'Customer'},
            {value: 'vendor', label: 'Vendor'}
        ],
        search: '',
        resultKey: _.uniqueId('resultKey_'),
        systemCurrency: null,
        summary: null,
        tableOptions: {
            rowHeight: 36
        },
        isInitialized: false
    }),

    computed: {
        payloadSchema() {
            return {
                startDate: {
                    type: 'date',
                    label: 'Start date',
                    required: false
                },
                endDate: {
                    type: 'date',
                    label: 'End date',
                    required: false
                },
                partnerTypes: {
                    type: ['string'],
                    label: 'Partner types',
                    required: false
                },
                partnerGroupIds: {
                    type: ['string'],
                    label: 'Partner groups',
                    required: false
                },
                partnerTags: {
                    type: ['string'],
                    label: 'Partner tags',
                    required: false
                },
                partnerId: {
                    type: 'string',
                    label: 'Partner',
                    required: false
                },
                currencyIds: {
                    type: ['string'],
                    label: 'Currencies',
                    required: false
                },
                branchIds: {
                    type: ['string'],
                    label: 'Branches',
                    required: false
                },
                financialProjectIds: {
                    type: ['string'],
                    label: 'Projects',
                    required: false
                },
                salespersonId: {
                    type: 'string',
                    label: 'Salesperson',
                    required: false
                },
                scope: {
                    type: 'string',
                    label: 'Scope',
                    required: false
                },
                groupByCurrency: {
                    type: 'boolean',
                    label: 'Group by currency',
                    default: false
                },
                groupByBranch: {
                    type: 'boolean',
                    label: 'Group by branch',
                    default: false
                },
                groupByProject: {
                    type: 'boolean',
                    label: 'Group by project',
                    default: false
                },
                groupBySalesperson: {
                    type: 'boolean',
                    label: 'Group by salesperson',
                    default: false
                },
                hideZeroBalances: {
                    type: 'boolean',
                    label: 'Hide Zero Balances',
                    default: false
                }
            };
        },
        scopeOptions() {
            return [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];
        },
        partnerTagsOptions() {
            return this.partnerTags.map(tag => ({value: tag.label, label: tag.label}));
        },
        columns() {
            const payload = this.payload;
            const vm = this;

            return [
                {field: 'partnerCode', label: 'Partner code', width: 150},
                {field: 'partnerName', label: 'Partner name', minWidth: 180},
                {field: 'partnerTinIdentity', label: 'TIN / Identity', width: 180, visible: false},
                ...(!!payload.groupBySalesperson ? [{field: 'salespersonName', label: 'Salesperson', width: 180}] : []),
                ...(!!payload.groupByBranch ? [{field: 'branchName', label: 'Branch office', width: 180}] : []),
                ...(!!payload.groupByProject ? [{field: 'financialProjectName', label: 'Project', width: 180}] : []),
                ...(!!payload.groupByCurrency ? [{field: 'currencyName', label: 'Currency', width: 75}] : []),
                {field: 'debit', label: 'Debit', format: 'currency', width: 150},
                {field: 'credit', label: 'Credit', format: 'currency', width: 150},
                {
                    field: 'balance',
                    label: 'Balance',
                    format: 'currency',
                    width: 150,
                    visibleForExport: true,
                    hidden: () => _.isDate(payload.startDate)
                },
                {
                    field: 'initialBalance',
                    label: 'Initial balance',
                    format: 'currency',
                    width: 150,
                    visibleForExport: true,
                    hidden: () => !(_.isDate(payload.startDate))
                },
                {
                    field: 'periodBalance',
                    label: 'Period balance',
                    format: 'currency',
                    width: 150,
                    visibleForExport: true,
                    hidden: () => !(_.isDate(payload.startDate))
                },
                {
                    field: 'totalBalance',
                    label: 'Total balance',
                    format: 'currency',
                    width: 150,
                    visibleForExport: true,
                    hidden: () => !(_.isDate(payload.startDate))
                },
                {
                    headerName: '#',
                    field: 'popup-edit-cell',
                    width: 35,
                    maxWidth: 35,
                    pinned: 'right',
                    lockPosition: true,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    cellClass: params => {
                        if (params.node.rowPinned) {
                            return '';
                        }

                        return 'popup-edit-cell';
                    },
                    headerClass: 'popup-edit-header-cell',
                    resizable: false,
                    suppressNavigable: true,
                    suppressMovable: true,
                    suppressSizeToFit: true,
                    sortable: false,
                    suppressMenu: true,
                    suppressColumnsToolPanel: true,
                    headerComponentParams: {
                        template:
                            '<div class="ag-cell-label-container" role="presentation">' +
                            '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                            '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                            `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                            '  </div>' +
                            '</div>'
                    },
                    cellRenderer(params) {
                        if (params.node.rowPinned) {
                            return '';
                        }

                        return '<i class="fal fa-arrow-right"></i>';
                    },
                    onCellClicked(params) {
                        if (params.node.rowPinned) {
                            return '';
                        }

                        const data = params.data;
                        if (
                            _.isString(data.partnerCode)
                        ) {
                            vm.$shell.openProgram('accounting.reports.partner-ledger', {
                                searchQuery: data.partnerCode
                            });
                        }
                        return false;
                    }
                },
                ...(!!payload.groupByCurrency
                    ? [
                          {
                              field: 'debitFC',
                              label: 'Debit (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150
                          },
                          {
                              field: 'creditFC',
                              label: 'Credit (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150
                          },
                          {
                              field: 'balanceFC',
                              label: 'Balance (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150,
                              hidden: () => _.isDate(payload.startDate)
                          },
                          {
                              field: 'initialBalanceFC',
                              label: 'Initial balance (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150,
                              hidden: () => !_.isDate(payload.startDate)
                          },
                          {
                              field: 'periodBalanceFC',
                              label: 'Period balance (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150,
                              hidden: () => !_.isDate(payload.startDate)
                          },
                          {
                              field: 'totalBalanceFC',
                              label: 'Total balance (FC)',
                              format: 'currency',
                              formatOptions: data => {
                                  let options = {currency: {}};

                                  if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                                      const currency = this.currencies.find(c => c._id === data.currencyId);

                                      if (!!currency) {
                                          options.currency.symbol = currency.symbol;
                                          options.currency.format =
                                              currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                                      }
                                  }

                                  return options;
                              },
                              width: 150,
                              hidden: () => !_.isDate(payload.startDate)
                          }
                      ]
                    : [])
            ];
        }
    },

    methods: {
        handlePayloadChange(payload, field) {
            setTimeout(() => {
                this.resultKey = _.uniqueId('resultKey_');
            }, 75);
        },
        async getRows(query, params) {
            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const sort = query.$sort || {partnerCode: -1};
            const skip = startRow;
            const limit = endRow - startRow;
            const result = {total: 0, data: []};

            query = rawMongoQuery(query);
            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }
            query.$and.push({partnerType: !!this.$params('type') ? this.$params('type') : {$ne: 'employee'}});

            const report = await this.$rpc('accounting.partner-balances-records', {
                ...this.payload,
                query,
                sort,
                skip,
                limit
            });

            this.summary = report.summaryResult;

            result.total = report.tableResult.total;
            result.data = report.tableResult.data;

            return result;
        },
        async summaryRow() {
            return {rowCount: 0, ...this.summary};
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];

        this.currencies = await this.$collection('kernel.currencies').find({});
        this.partnerTags = await this.$rpc('partners.get-partner-tags');
        this.systemCurrency = company.currency;
        this.isInitialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.accounting-reports-partner-balances {
    position: relative;
    display: flex;
    flex-flow: row nowrap;
    width: 100%;
    height: 100%;
    background-color: white;
    overflow: hidden;

    .partner-balances-left {
        flex: 0 0 270px;
        border-right: 1px solid $border-color;
        background-color: $light-25;
        height: 100%;

        .el-scrollbar {
            width: 100%;
            height: 100%;
        }

        .ui-form {
            background-color: transparent;
        }
    }

    .partner-balances-content {
        position: relative;
        flex: 1 1 0;
        height: 100%;
        overflow: hidden;

        .ui-table:not(.is-editable) {
            .ag-header-row.ag-header-row-column {
                border-top: none;
            }

            .ag-cell {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                font-size: 14px;
                line-height: 1.4;
                padding: 0 12px;

                &.is-number-cell {
                    justify-content: flex-end;

                    .ui-table-relation-renderer {
                        justify-content: flex-end;
                    }

                    .relation-renderer-icon {
                        margin-right: 4px;
                    }

                    .relation-renderer-label {
                        flex-grow: initial;
                        flex-shrink: initial;
                        flex-basis: initial;
                    }
                }

                .ag-cell-wrapper.ag-row-group {
                    flex: 1 1 0;
                }
            }
        }

        .ui-table:not(.is-editable) .ag-floating-bottom-viewport .ag-floating-bottom-container .ag-row,
        .ui-table:not(.is-editable) .ag-pinned-left-floating-bottom .ag-row,
        .ui-table:not(.is-editable) .ag-pinned-right-floating-bottom .ag-row {
            height: 100% !important;
        }

        .ui-table:not(.is-editable) .ag-floating-bottom-viewport .ag-floating-bottom-container .ag-row .ag-cell,
        .ui-table:not(.is-editable) .ag-pinned-left-floating-bottom .ag-row .ag-cell,
        .ui-table:not(.is-editable) .ag-pinned-right-floating-bottom .ag-row .ag-cell {
            height: 100% !important;
            line-height: 36px !important;
        }
    }
}
</style>
