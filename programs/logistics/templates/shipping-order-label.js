import _ from 'lodash';
import fs from 'fs-extra';
import path from 'path';
import {DOMImplementation, XMLSerializer} from '@xmldom/xmldom';
import JsBarcode from 'jsbarcode';
import sharp from 'sharp';

export default {
    name: 'shipping-order-label',
    title: 'Shipping Order Label',
    contentType: 'html',
    outputType: 'pdf',
    outputFormat: 'custom',
    outputWidth: 78.7,
    outputHeight: 111.6,
    async getLocale(app, recordId) {
        const company = await app.collection('kernel.company').findOne({});

        return company.language.isoCode;
    },
    async record(app, recordId, params) {
        const so = await app.collection('logistics.shipping-orders').get(recordId);
        const company = await app.collection('kernel.company').findOne({});
        const soItems = Array.isArray(so.items) ? so.items : [];
        const items = [];

        const data = {};
        data.code = so.code;
        data.partnerName = so.partnerName;
        data.partnerPhone = so.partnerPhone;
        data.partnerEmail = so.partnerEmail;
        data.carrierName = so.carrierName;
        data.recordDate = app.format(so.recordDate, 'date');

        const shippingPaymentTypeOptions = [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ];
        const shippingPaymentType = shippingPaymentTypeOptions.find(
            option => option.value === so.shippingPaymentType
        )?.label;

        if (shippingPaymentType) {
            data.shippingPaymentType = app.translate(shippingPaymentType);
        }

        data.companyWebsite = company.website;
        data.companyLegalName = company.legalName;
        data.companyPhone = company.phone;
        data.companyAddress = company.address;

        if (data.companyAddress && data.companyAddress.countryId) {
            data.companyAddress.country =
                (
                    (await app.collection('kernel.countries').findOne({
                        _id: data.companyAddress.countryId,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    })) || {}
                )?.name ?? '';

            delete data.companyAddress.countryId;
        }

        const saleOrder = await app.collection('sale.orders').findOne({
            _id: so.orderId,
            $select: ['code'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        data.orderCode = saleOrder?.code ?? '';

        const deliveryAddress = {};
        if (_.isPlainObject(so.deliveryAddress)) {
            deliveryAddress.country = (
                (await app.collection('kernel.countries').findOne({
                    _id: so.deliveryAddress.countryId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            deliveryAddress.address = so.deliveryAddress.address;
            deliveryAddress.city = so.deliveryAddress.city;
            deliveryAddress.district = so.deliveryAddress.district;
            deliveryAddress.subDistrict = so.deliveryAddress.subDistrict;
            deliveryAddress.street = so.deliveryAddress.street;
            deliveryAddress.doorNumber = so.deliveryAddress.doorNumber;
            deliveryAddress.apartmentNumber = so.deliveryAddress.apartmentNumber;
            deliveryAddress.postalCode = so.deliveryAddress.postalCode;
        }
        data.deliveryAddress = deliveryAddress;

        data.topAddressLine = '';
        if (!!so.deliveryAddress.street && so.deliveryAddress.street !== '') {
            data.topAddressLine += so.deliveryAddress.street;
        }
        if (
            !!so.deliveryAddress.apartmentNumber &&
            so.deliveryAddress.apartmentNumber !== '' &&
            !!so.deliveryAddress.doorNumber &&
            so.deliveryAddress.doorNumber !== ''
        ) {
            data.topAddressLine += `No: ${so.deliveryAddress.apartmentNumber}/${so.deliveryAddress.doorNumber}`;
        } else if (!!so.deliveryAddress.apartmentNumber && so.deliveryAddress.apartmentNumber !== '') {
            data.topAddressLine += `No: ${so.deliveryAddress.apartmentNumber}`;
        } else if (!!so.deliveryAddress.doorNumber && so.deliveryAddress.doorNumber !== '') {
            data.topAddressLine += `No: ${so.deliveryAddress.doorNumber}`;
        }
        data.middleAddressLine = !!so.deliveryAddress.subDistrict ? so.deliveryAddress.subDistrict : '';
        data.bottomAddressLine = `${so.deliveryAddress.district} / ${so.deliveryAddress.city}`;

        if (soItems.length > 0) {
            for (const soItem of soItems) {
                const item = {
                    ...data,
                    barcode: soItem.barcode
                };
                const xmlSerializer = new XMLSerializer();
                const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
                const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

                JsBarcode(svgNode, so.code, {
                    xmlDocument: document,
                    height: 120,
                    margin: 0,
                    background: '',
                    displayValue: false
                });

                const base64 = Buffer.from(
                    await sharp(Buffer.from(xmlSerializer.serializeToString(svgNode)))
                        .png()
                        .toBuffer()
                ).toString('base64');

                item.barcodeImageUrl = `data:image/png;base64,${base64}`;
                item.volumetricWeight = soItem.volumetricWeight;

                const pkg = await app.collection('logistics.packages').findOne({
                    _id: soItem.packageId,
                    $select: ['items'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                const subItems = [];
                if (pkg && Array.isArray(pkg.items) && pkg.items.length > 0) {
                    for (const packageItem of pkg.items) {
                        subItems.push({
                            productCode: packageItem.productCode,
                            productDefinition: packageItem.productDefinition,
                            quantity: packageItem.quantity,
                            unit: packageItem.unitName
                        });
                    }
                }

                item.subItems = subItems;

                items.push(item);
            }
        } else {
            const item = {
                ...data,
                barcode: data.code
            };
            const xmlSerializer = new XMLSerializer();
            const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
            const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

            JsBarcode(svgNode, so.code, {
                xmlDocument: document,
                height: 120,
                margin: 0,
                background: '',
                displayValue: false
            });

            const base64 = Buffer.from(
                await sharp(Buffer.from(xmlSerializer.serializeToString(svgNode)))
                    .png()
                    .toBuffer()
            ).toString('base64');

            item.barcodeImageUrl = `data:image/png;base64,${base64}`;

            items.push(item);
        }

        if (so.printingStatus !== 'printed') {
            await app.collection('logistics.shipping-orders').bulkWrite([
                {
                    updateOne: {
                        filter: {_id: so._id},
                        update: {
                            $set: {
                                printingStatus: 'printed',
                                printedAt: app.datetime.local().toJSDate()
                            }
                        }
                    }
                }
            ]);
        }

        return {items};
    },
    async sample(app, params) {
        const xmlSerializer = new XMLSerializer();
        const document = new DOMImplementation().createDocument('http://www.w3.org/1999/xhtml', 'html', null);
        const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

        JsBarcode(svgNode, '1234567891123456', {
            xmlDocument: document,
            height: 120,
            margin: 0,
            background: '',
            displayValue: false
        });

        const base64 = Buffer.from(
            await sharp(Buffer.from(xmlSerializer.serializeToString(svgNode)))
                .png()
                .toBuffer()
        ).toString('base64');
        const barcodeImageUrl = `data:image/png;base64,${base64}`;

        const deliveryAddress = {};
        deliveryAddress.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        deliveryAddress.address = 'Cevizli Mahallesi Tugay Yolu Caddesi No:20/A Maltepe/İstanbul';
        deliveryAddress.city = 'İstanbul';
        deliveryAddress.district = 'Maltepe';
        deliveryAddress.subDistrict = 'Cevizli Mahallesi';
        deliveryAddress.street = 'Tugay Yolu Caddesi';
        deliveryAddress.doorNumber = '20/A';
        deliveryAddress.apartmentNumber = '10';
        deliveryAddress.postalCode = '34846';

        const companyAddress = {};
        companyAddress.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        companyAddress.address = 'Cevizli Mahallesi Tugay Yolu Caddesi No:20/A Maltepe/İstanbul';
        companyAddress.city = 'İstanbul';
        companyAddress.district = 'Maltepe';
        companyAddress.subDistrict = 'Cevizli Mahallesi';
        companyAddress.street = 'Tugay Yolu Caddesi';
        companyAddress.doorNumber = '20/A';
        companyAddress.apartmentNumber = '10';
        companyAddress.postalCode = '34846';

        const subItems = [
            {
                productCode: 'P000001',
                productDefinition: 'Test product - 1',
                quantity: 1,
                unit: 'Adet'
            },
            {
                productCode: 'P000002',
                productDefinition: 'Test product - 2',
                quantity: 3,
                unit: 'Adet'
            }
        ];

        return {
            items: [
                {
                    code: '1234567891123456',
                    barcode: '8234567891123456',
                    topAddressLine: 'Test street Test Apt. No:14/5',
                    middleAddressLine: 'Test Sub-district',
                    bottomAddressLine: 'Test District / Test City',
                    recordDate: app.format(app.datetime.local().toJSDate(), 'date'),
                    deliveryAddress: deliveryAddress,
                    companyAddress: companyAddress,
                    subItems: subItems,
                    shippingPaymentType: app.translate('Freight prepaid'),
                    volumetricWeight: 1,
                    orderCode: '********',
                    partnerName: 'Test Customer',
                    partnerPhone: '+90 555 111 11 11',
                    partnerEmail: '<EMAIL>',
                    carrierName: 'Test Carrier',
                    companyWebsite: 'www.entererp.com',
                    companyLegalName: 'EnterERP',
                    companyPhone: '+90 8505820035',
                    barcodeImageUrl
                },
                {
                    code: '1234567891123456',
                    barcode: '9234567891123456',
                    topAddressLine: 'Test street Test Apt. No:14/5',
                    middleAddressLine: 'Test Sub-district',
                    bottomAddressLine: 'Test District / Test City',
                    recordDate: app.format(app.datetime.local().toJSDate(), 'date'),
                    deliveryAddress: deliveryAddress,
                    companyAddress: companyAddress,
                    subItems: subItems,
                    shippingPaymentType: app.translate('Freight collect'),
                    volumetricWeight: 2,
                    orderCode: '********',
                    partnerName: 'Test Customer',
                    partnerPhone: '+90 555 111 11 11',
                    partnerEmail: '<EMAIL>',
                    carrierName: 'Test Carrier',
                    companyWebsite: 'www.entererp.com',
                    companyLegalName: 'EnterERP',
                    companyPhone: '+90 8505820035',
                    barcodeImageUrl
                }
            ]
        };
    },
    content: (app, recordId) =>
        fs.readFile(path.join(app.config('paths.static'), 'templates/logistics/shipping-order-label.hbs'), {
            encoding: 'utf8'
        })
};
