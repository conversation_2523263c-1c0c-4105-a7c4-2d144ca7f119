// Partner güncellendiğinde transaction'lardaki partner ob<PERSON><PERSON><PERSON> de güncelleyen yardımcı fonksiyon
export async function syncPartnerToTransactions(app, partnerId) {
    const partner = await app.collection('kernel.partners').findOne({_id: partnerId});
    if (!partner) return;

    const partnerObj = {
        _id: partner._id,
        code: partner.code,
        name: partner.name,
        type: partner.type,
        groupId: partner.groupId,
        tinIdentity: partner.tin || partner.identity,
        tags: Array.isArray(partner.tags) ? partner.tags.map(tag => tag.label) : []
    };

    await app.db.collection('accounting_transactions').updateMany(
        { partnerId: partnerId },
        { $set: { partner: partnerObj } }
    );
}
